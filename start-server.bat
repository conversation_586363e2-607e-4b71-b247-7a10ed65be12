@echo off
echo Starting local HTTP server for Sanction Screening Application...
echo.
echo This will start a local web server to enable full WCSPremium functionality.
echo The application will be available at: http://localhost:8000
echo.
echo Press Ctrl+C to stop the server when done.
echo.

REM Try Python 3 first
python -m http.server 8000 2>nul
if %errorlevel% neq 0 (
    REM Try Python 2 if Python 3 fails
    python -m SimpleHTTPServer 8000 2>nul
    if %errorlevel% neq 0 (
        REM Try Node.js if Python is not available
        npx http-server -p 8000 2>nul
        if %errorlevel% neq 0 (
            echo.
            echo ERROR: No suitable HTTP server found.
            echo.
            echo Please install one of the following:
            echo 1. Python (recommended): https://python.org
            echo 2. Node.js: https://nodejs.org
            echo.
            echo Or use the application in fallback mode by opening index.html directly.
            echo Note: WCSPremium will be limited to 10,000 records in fallback mode.
            echo.
            pause
        )
    )
)
