<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sanction Screening Application</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Sanction Screening Application</h1>
        </header>

        <nav class="tab-navigation">
            <button class="tab-button active" data-tab="on-demand">On-demand Screening</button>
            <button class="tab-button" data-tab="bulk">Bulk Screening</button>
            <button class="tab-button" data-tab="swift">SWIFT Screening</button>
            <button class="tab-button" data-tab="blacklist">Blacklist Upload</button>
        </nav>

        <main class="content">
            <!-- On-demand Screening Tab -->
            <div id="on-demand" class="tab-content active">
                <h2>On-demand Screening</h2>
                <div class="screening-form">
                    <div class="form-group">
                        <label>Search Type:</label>
                        <div class="search-type-buttons">
                            <button type="button" class="search-type-btn active" data-type="name">Name</button>
                            <button type="button" class="search-type-btn" data-type="nationality">Nationality</button>
                            <button type="button" class="search-type-btn" data-type="nrc">NRC</button>
                            <button type="button" class="search-type-btn" data-type="brn">BRN</button>
                            <button type="button" class="search-type-btn" data-type="entity">Entity Name</button>
                            <button type="button" class="search-type-btn" data-type="dob">Date of Birth</button>
                            <button type="button" class="search-type-btn" data-type="phone">Phone Number</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="search-value">Search Value:</label>
                        <input type="text" id="search-value" name="search-value" placeholder="Enter search value">
                    </div>

                    <div class="form-group">
                        <label for="match-type">Match Type:</label>
                        <select id="match-type" name="match-type">
                            <option value="exact">Exact Match</option>
                            <option value="partial">Partial Match</option>
                            <option value="fuzzy">Fuzzy Match</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="screen-btn" class="btn btn-primary">Screen</button>
                        <button type="button" id="clear-btn" class="btn btn-secondary">Clear</button>
                    </div>
                </div>

                <div class="results-section">
                    <h3>Screening Results</h3>
                    <div id="results-container">
                        <p class="no-results">No screening performed yet.</p>
                    </div>
                </div>
            </div>

            <!-- Bulk Screening Tab -->
            <div id="bulk" class="tab-content">
                <h2>Bulk Screening</h2>
                <div class="placeholder-content">
                    <p>Bulk screening functionality will be implemented later.</p>
                </div>
            </div>

            <!-- SWIFT Screening Tab -->
            <div id="swift" class="tab-content">
                <h2>SWIFT Screening</h2>
                <div class="placeholder-content">
                    <p>SWIFT screening functionality will be implemented later.</p>
                </div>
            </div>

            <!-- Blacklist Upload Configuration Tab -->
            <div id="blacklist" class="tab-content">
                <h2>Blacklist Upload Configuration</h2>
                <div class="upload-section">
                    <div class="upload-area">
                        <input type="file" id="blacklist-file" accept=".csv,.xlsx,.xls" style="display: none;">
                        <div class="upload-dropzone" id="upload-dropzone">
                            <div class="upload-icon">📁</div>
                            <p>Click to select file or drag and drop</p>
                            <p class="file-types">Supported formats: CSV, Excel (.xlsx, .xls)</p>
                        </div>
                    </div>

                    <div class="file-info" id="file-info" style="display: none;">
                        <h4>Selected File:</h4>
                        <p id="file-name"></p>
                        <p id="file-size"></p>
                    </div>

                    <div class="column-mapping" id="column-mapping" style="display: none;">
                        <h3>Column Mapping</h3>
                        <p>Map your file columns to the required blacklist fields:</p>
                        <div class="mapping-container" id="mapping-container">
                            <!-- Column mapping will be generated dynamically -->
                        </div>
                    </div>

                    <div class="upload-actions" id="upload-actions" style="display: none;">
                        <button type="button" id="upload-btn" class="btn btn-primary">Upload Blacklist</button>
                        <button type="button" id="cancel-upload-btn" class="btn btn-secondary">Cancel</button>
                    </div>
                </div>

                <div class="blacklist-management">
                    <h3>Current Blacklist</h3>
                    <div class="blacklist-stats">
                        <span id="blacklist-count">0 records</span>
                        <button type="button" id="clear-blacklist-btn" class="btn btn-danger">Clear All</button>
                    </div>
                    <div class="blacklist-table-container">
                        <table id="blacklist-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>PENID</th>
                                    <th>Name</th>
                                    <th>Customer ID</th>
                                    <th>NRC/BRN</th>
                                    <th>Industry</th>
                                    <th>Nationality</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Blacklist data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
