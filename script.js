// Sanction Screening Application JavaScript

class SanctionScreeningApp {
    constructor() {
        this.blacklistData = this.loadBlacklistData();
        this.requiredColumns = [
            'PENID', 'Reporting Date', 'Name', 'Customer ID', 'NRC/BRN', 'Industry',
            'Segment', 'Group Code', 'Reporting Bank', 'Rating', 'Location',
            'Customer Address 1', 'Customer Address 2', 'Phone 1', 'Phone 2',
            'DOB/DOI', 'Date Field 2', 'GST', 'CIN', 'DIN', 'Nationality',
            'Passport', 'Driver\'s License', 'Aadhaar(UIDAI)', 'Voter ID',
            'Ration Card', 'Remarks', 'Text Field 1', 'Text Field 2',
            'Text Field 3', 'I/E', 'Active / Inactive', 'Inactivation Date'
        ];

        // WCSPremium integration
        this.wcsWorker = null;
        this.wcsStatus = {
            isLoaded: false,
            isLoading: false,
            totalRecords: 0,
            error: null
        };
        this.pendingSearches = new Map(); // Track concurrent searches

        this.init();
    }

    init() {
        this.setupTabNavigation();
        this.setupOnDemandScreening();
        this.setupBlacklistUpload();
        this.setupWCSPremium();
        this.updateBlacklistDisplay();
        this.updateWCSStatus();
    }

    // Tab Navigation
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                
                // Remove active class from all tabs and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                button.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }

    // On-demand Screening
    setupOnDemandScreening() {
        const screenBtn = document.getElementById('screen-btn');
        const clearBtn = document.getElementById('clear-btn');
        const searchValue = document.getElementById('search-value');
        const searchTypeButtons = document.querySelectorAll('.search-type-btn');

        screenBtn.addEventListener('click', () => this.performScreening());
        clearBtn.addEventListener('click', () => this.clearScreeningForm());

        // Search type button handling
        searchTypeButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                searchTypeButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
            });
        });

        // Enter key support
        searchValue.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performScreening();
            }
        });
    }

    performScreening() {
        const activeSearchTypeBtn = document.querySelector('.search-type-btn.active');
        const searchType = activeSearchTypeBtn ? activeSearchTypeBtn.getAttribute('data-type') : 'name';
        const searchValue = document.getElementById('search-value').value.trim();
        const matchType = document.getElementById('match-type').value;
        const resultsContainer = document.getElementById('results-container');

        if (!searchValue) {
            this.showError('Please enter a search value');
            return;
        }

        // Show loading
        resultsContainer.innerHTML = '<div class="loading"></div><p>Screening in progress...</p>';

        // Perform dual search (blacklist + WCSPremium)
        this.performDualSearch(searchType, searchValue, matchType);
    }

    searchBlacklist(searchType, searchValue, matchType) {
        const results = [];
        const searchLower = searchValue.toLowerCase();

        // Filter valid records for searching
        const validRecords = this.blacklistData.filter(record =>
            record && (record.Name || record.PENID || record['Customer ID'])
        );

        validRecords.forEach((record, index) => {
            let fieldValue = '';
            
            switch (searchType) {
                case 'name':
                    fieldValue = record.Name || '';
                    break;
                case 'nationality':
                    fieldValue = record.Nationality || '';
                    break;
                case 'nrc':
                case 'brn':
                    fieldValue = record['NRC/BRN'] || '';
                    break;
                case 'entity':
                    fieldValue = record.Name || ''; // Assuming entity name is in Name field
                    break;
                case 'dob':
                    fieldValue = record['DOB/DOI'] || '';
                    break;
                case 'phone':
                    fieldValue = (record['Phone 1'] || '') + ' ' + (record['Phone 2'] || '');
                    break;
            }

            fieldValue = fieldValue.toLowerCase();

            let isMatch = false;
            switch (matchType) {
                case 'exact':
                    isMatch = fieldValue === searchLower;
                    break;
                case 'partial':
                    isMatch = fieldValue.includes(searchLower);
                    break;
                case 'fuzzy':
                    isMatch = this.fuzzyMatch(fieldValue, searchLower);
                    break;
            }

            if (isMatch) {
                results.push({
                    ...record,
                    matchScore: this.calculateMatchScore(fieldValue, searchLower, matchType)
                });
            }
        });

        return results.sort((a, b) => b.matchScore - a.matchScore);
    }

    fuzzyMatch(str1, str2) {
        const threshold = 0.7;
        const similarity = this.calculateSimilarity(str1, str2);
        return similarity >= threshold;
    }

    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    calculateMatchScore(fieldValue, searchValue, matchType) {
        switch (matchType) {
            case 'exact':
                return fieldValue === searchValue ? 100 : 0;
            case 'partial':
                return fieldValue.includes(searchValue) ? 80 : 0;
            case 'fuzzy':
                return Math.round(this.calculateSimilarity(fieldValue, searchValue) * 100);
            default:
                return 0;
        }
    }

    displayScreeningResults(results, searchType, searchValue) {
        const resultsContainer = document.getElementById('results-container');
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="status-success">
                    <h4>✓ No Matches Found</h4>
                    <p>No records found matching "${searchValue}" in ${searchType} field.</p>
                    <p>This entity appears to be clear for processing.</p>
                </div>
            `;
            return;
        }

        let html = `
            <div class="status-warning">
                <h4>⚠ Potential Matches Found</h4>
                <p>${results.length} record(s) found matching "${searchValue}" in ${searchType} field.</p>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Match Score</th>
                        <th>Name</th>
                        <th>PENID</th>
                        <th>Customer ID</th>
                        <th>NRC/BRN</th>
                        <th>Nationality</th>
                        <th>Industry</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
        `;

        results.forEach(record => {
            const status = record['Active / Inactive'] || 'Unknown';
            const statusClass = status.toLowerCase() === 'active' ? 'status-error' : 'status-warning';
            
            html += `
                <tr>
                    <td><span class="status-warning">${record.matchScore}%</span></td>
                    <td>${record.Name || 'N/A'}</td>
                    <td>${record.PENID || 'N/A'}</td>
                    <td>${record['Customer ID'] || 'N/A'}</td>
                    <td>${record['NRC/BRN'] || 'N/A'}</td>
                    <td>${record.Nationality || 'N/A'}</td>
                    <td>${record.Industry || 'N/A'}</td>
                    <td><span class="${statusClass}">${status}</span></td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        resultsContainer.innerHTML = html;
    }

    clearScreeningForm() {
        // Reset search type buttons
        const searchTypeButtons = document.querySelectorAll('.search-type-btn');
        searchTypeButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelector('.search-type-btn[data-type="name"]').classList.add('active');

        document.getElementById('search-value').value = '';
        document.getElementById('match-type').value = 'exact';
        document.getElementById('results-container').innerHTML = '<p class="no-results">No screening performed yet.</p>';
    }

    // WCSPremium Setup and Management
    setupWCSPremium() {
        try {
            // Initialize Web Worker
            this.wcsWorker = new Worker('wcs-worker.js');

            // Set up worker message handlers
            this.wcsWorker.onmessage = (event) => {
                this.handleWCSWorkerMessage(event.data);
            };

            this.wcsWorker.onerror = (error) => {
                console.error('WCS Worker error:', error);
                this.wcsStatus.error = 'Worker initialization failed';
                this.updateWCSStatus();
            };

            // Try to auto-load WCSPremium file
            this.autoLoadWCSPremium();

        } catch (error) {
            console.error('Failed to initialize WCS Worker:', error);
            this.wcsStatus.error = 'Web Worker not supported';
            this.updateWCSStatus();
        }
    }

    // Handle messages from WCS Worker
    handleWCSWorkerMessage(message) {
        const { type, data } = message;

        switch (type) {
            case 'LOAD_STARTED':
                this.wcsStatus.isLoading = true;
                this.wcsStatus.error = null;
                this.updateWCSStatus();
                this.showSuccess(`Loading WCSPremium file: ${data.fileName}`);
                break;

            case 'LOAD_PROGRESS':
                this.wcsStatus.progress = data.progress;
                this.updateWCSStatus();
                break;

            case 'LOAD_COMPLETED':
                this.wcsStatus.isLoading = false;
                this.wcsStatus.isLoaded = true;
                this.wcsStatus.totalRecords = data.totalRecords;
                this.updateWCSStatus();
                this.showSuccess(`WCSPremium loaded: ${data.totalRecords.toLocaleString()} records`);
                break;

            case 'SEARCH_COMPLETED':
                this.handleWCSSearchResults(data);
                break;

            case 'ERROR':
                this.wcsStatus.isLoading = false;
                this.wcsStatus.error = data.message;
                this.updateWCSStatus();
                this.showError(`WCSPremium error: ${data.message}`);
                break;

            case 'DATA_CLEARED':
                this.wcsStatus.isLoaded = false;
                this.wcsStatus.totalRecords = 0;
                this.updateWCSStatus();
                break;
        }
    }

    // Auto-load WCSPremium file if it exists
    async autoLoadWCSPremium() {
        try {
            // Try to load the premium-world-check file
            const response = await fetch('lists/premium-world-check.csv');
            if (response.ok) {
                const blob = await response.blob();
                const file = new File([blob], 'premium-world-check.csv', { type: 'text/csv' });
                this.loadWCSPremiumFile(file);
            }
        } catch (error) {
            console.log('WCSPremium file not found or not accessible via fetch');
            // This is expected in many cases, so we don't show an error
        }
    }

    // Load WCSPremium file into worker
    loadWCSPremiumFile(file) {
        if (!this.wcsWorker) {
            this.showError('WCS Worker not available');
            return;
        }

        this.wcsWorker.postMessage({
            type: 'LOAD_FILE',
            data: { file: file }
        });
    }

    // Perform dual search across both blacklist and WCSPremium
    async performDualSearch(searchType, searchValue, matchType) {
        const searchId = Date.now().toString();
        this.pendingSearches.set(searchId, {
            searchType,
            searchValue,
            matchType,
            blacklistResults: null,
            wcsResults: null,
            startTime: Date.now()
        });

        // Search regular blacklist
        const blacklistResults = this.searchBlacklist(searchType, searchValue, matchType);
        this.pendingSearches.get(searchId).blacklistResults = blacklistResults;

        // Search WCSPremium if available
        if (this.wcsStatus.isLoaded && this.wcsWorker) {
            this.wcsWorker.postMessage({
                type: 'SEARCH',
                data: { searchType, searchValue, matchType, searchId }
            });
        } else {
            // No WCS data available, display blacklist results only
            this.pendingSearches.get(searchId).wcsResults = [];
            this.displayDualSearchResults(searchId);
        }
    }

    // Handle WCS search results
    handleWCSSearchResults(data) {
        const searchId = data.searchId || this.getLatestSearchId();
        const search = this.pendingSearches.get(searchId);

        if (search) {
            search.wcsResults = data.results || [];
            this.displayDualSearchResults(searchId);
        }
    }

    // Get the latest search ID
    getLatestSearchId() {
        const searches = Array.from(this.pendingSearches.keys());
        return searches[searches.length - 1];
    }

    // Display combined results from both sources
    displayDualSearchResults(searchId) {
        const search = this.pendingSearches.get(searchId);
        if (!search || search.blacklistResults === null || search.wcsResults === null) {
            return; // Wait for both searches to complete
        }

        const { searchType, searchValue, blacklistResults, wcsResults } = search;
        const resultsContainer = document.getElementById('results-container');

        // Mark blacklist results with source
        const blacklistWithSource = blacklistResults.map(result => ({
            ...result,
            source: 'Blacklist'
        }));

        // Combine and sort results
        const allResults = [...blacklistWithSource, ...wcsResults];
        allResults.sort((a, b) => b.matchScore - a.matchScore);

        // Display results
        this.displayCombinedResults(allResults, searchType, searchValue);

        // Clean up
        this.pendingSearches.delete(searchId);
    }

    // Update WCS status display
    updateWCSStatus() {
        // Update main status indicator
        const wcsStatusElement = document.getElementById('wcs-status');
        const wcsDetailedStatus = document.getElementById('wcs-detailed-status');
        const wcsRecordCount = document.getElementById('wcs-record-count');
        const wcsProgress = document.getElementById('wcs-progress');
        const wcsProgressFill = document.getElementById('wcs-progress-fill');
        const wcsLoadingProgress = document.getElementById('wcs-loading-progress');
        const wcsLoadingFill = document.getElementById('wcs-loading-fill');
        const wcsLoadingText = document.getElementById('wcs-loading-text');

        if (this.wcsStatus.error) {
            wcsStatusElement.textContent = 'Error';
            wcsStatusElement.className = 'status-indicator error';
            wcsDetailedStatus.textContent = this.wcsStatus.error;
            wcsProgress.style.display = 'none';
            wcsLoadingProgress.style.display = 'none';
        } else if (this.wcsStatus.isLoading) {
            wcsStatusElement.textContent = 'Loading...';
            wcsStatusElement.className = 'status-indicator loading';
            wcsDetailedStatus.textContent = 'Loading WCSPremium data...';
            wcsLoadingProgress.style.display = 'block';

            if (this.wcsStatus.progress) {
                wcsLoadingFill.style.width = this.wcsStatus.progress + '%';
                wcsLoadingText.textContent = `Loading WCSPremium data... ${Math.round(this.wcsStatus.progress)}%`;
            }
        } else if (this.wcsStatus.isLoaded) {
            wcsStatusElement.textContent = 'Loaded';
            wcsStatusElement.className = 'status-indicator loaded';
            wcsDetailedStatus.textContent = 'Ready for screening';
            wcsRecordCount.textContent = this.wcsStatus.totalRecords.toLocaleString();
            wcsProgress.style.display = 'none';
            wcsLoadingProgress.style.display = 'none';
        } else {
            wcsStatusElement.textContent = 'Not Loaded';
            wcsStatusElement.className = 'status-indicator';
            wcsDetailedStatus.textContent = 'WCSPremium data not loaded';
            wcsRecordCount.textContent = '0';
            wcsProgress.style.display = 'none';
            wcsLoadingProgress.style.display = 'none';
        }

        // Update blacklist status
        const blacklistStatus = document.getElementById('blacklist-status');
        const validRecords = this.blacklistData.filter(record =>
            record && (record.Name || record.PENID || record['Customer ID'])
        );
        blacklistStatus.textContent = `Ready (${validRecords.length.toLocaleString()} records)`;
        blacklistStatus.className = 'status-indicator ready';
    }

    // Clear WCS data
    clearWCSData() {
        if (confirm('Are you sure you want to clear WCSPremium data? This will require reloading the file.')) {
            if (this.wcsWorker) {
                this.wcsWorker.postMessage({ type: 'CLEAR_DATA', data: {} });
            }
            this.wcsStatus.isLoaded = false;
            this.wcsStatus.totalRecords = 0;
            this.wcsStatus.error = null;
            this.updateWCSStatus();
            this.showSuccess('WCSPremium data cleared');
        }
    }

    // Display combined results from both sources
    displayCombinedResults(allResults, searchType, searchValue) {
        const resultsContainer = document.getElementById('results-container');

        if (allResults.length === 0) {
            resultsContainer.innerHTML = `
                <div class="status-success">
                    <h4>✓ No Matches Found</h4>
                    <p>No records found matching "${searchValue}" in ${searchType} field across both data sources.</p>
                    <p>This entity appears to be clear for processing.</p>
                </div>
            `;
            return;
        }

        // Group results by source
        const blacklistResults = allResults.filter(r => r.source === 'Blacklist');
        const wcsResults = allResults.filter(r => r.source === 'WCSPremium');

        let html = `
            <div class="status-warning">
                <h4>⚠ Potential Matches Found</h4>
                <p>Found ${allResults.length} record(s) matching "${searchValue}" in ${searchType} field.</p>
                <div class="source-summary">
                    <span class="source-count blacklist">Blacklist: ${blacklistResults.length}</span>
                    <span class="source-count wcs">WCSPremium: ${wcsResults.length}</span>
                </div>
            </div>
        `;

        // Display results table
        html += this.generateResultsTable(allResults);
        resultsContainer.innerHTML = html;
    }

    // Generate results table HTML
    generateResultsTable(results) {
        let html = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Source</th>
                        <th>Match Score</th>
                        <th>Name</th>
                        <th>ID/UID</th>
                        <th>Category</th>
                        <th>Nationality</th>
                        <th>Status</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
        `;

        results.forEach(record => {
            const source = record.source || 'Unknown';
            const sourceClass = source === 'Blacklist' ? 'source-blacklist' : 'source-wcs';

            // Handle different field names between sources
            const name = record.Name ||
                        (record['LAST NAME'] && record['FIRST NAME'] ?
                         `${record['LAST NAME']}, ${record['FIRST NAME']}` :
                         record['LAST NAME'] || record['FIRST NAME']) || 'N/A';

            const id = record.PENID || record['Customer ID'] || record.UID || 'N/A';
            const category = record.Industry || record.CATEGORY || 'N/A';
            const nationality = record.Nationality || record.CITIZENSHIP || record.COUNTRIES || 'N/A';
            const status = record['Active / Inactive'] || 'Unknown';
            const statusClass = status.toLowerCase() === 'active' ? 'status-error' : 'status-warning';

            html += `
                <tr>
                    <td><span class="source-badge ${sourceClass}">${source}</span></td>
                    <td><span class="match-score">${record.matchScore}%</span></td>
                    <td>${name}</td>
                    <td>${id}</td>
                    <td>${category}</td>
                    <td>${nationality}</td>
                    <td><span class="${statusClass}">${status}</span></td>
                    <td><button class="btn btn-sm btn-secondary" onclick="app.showRecordDetails('${record.source}', '${id}')">View</button></td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        return html;
    }

    // Show detailed record information
    showRecordDetails(source, id) {
        // This would open a modal or detailed view
        // For now, just show an alert with basic info
        alert(`Detailed view for ${source} record ${id} would be implemented here.`);
    }

    // Blacklist Upload
    setupBlacklistUpload() {
        const fileInput = document.getElementById('blacklist-file');
        const dropzone = document.getElementById('upload-dropzone');
        const uploadBtn = document.getElementById('upload-btn');
        const cancelBtn = document.getElementById('cancel-upload-btn');
        const clearBlacklistBtn = document.getElementById('clear-blacklist-btn');
        const cleanBlacklistBtn = document.getElementById('clean-blacklist-btn');

        // WCS Premium controls
        const loadWcsBtn = document.getElementById('load-wcs-btn');
        const reloadWcsBtn = document.getElementById('reload-wcs-btn');
        const clearWcsBtn = document.getElementById('clear-wcs-btn');
        const wcsFileInput = document.getElementById('wcs-file-input');

        // File input and dropzone
        dropzone.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));

        // Drag and drop
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('dragover');
        });

        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('dragover');
        });

        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files[0]);
        });

        // Buttons
        uploadBtn.addEventListener('click', () => this.processBlacklistUpload());
        cancelBtn.addEventListener('click', () => this.cancelUpload());
        clearBlacklistBtn.addEventListener('click', () => this.clearBlacklist());
        cleanBlacklistBtn.addEventListener('click', () => this.cleanBlacklistData());

        // WCS Premium buttons
        loadWcsBtn.addEventListener('click', () => wcsFileInput.click());
        reloadWcsBtn.addEventListener('click', () => this.autoLoadWCSPremium());
        clearWcsBtn.addEventListener('click', () => this.clearWCSData());
        wcsFileInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                this.loadWCSPremiumFile(e.target.files[0]);
            }
        });
    }

    handleFileSelect(file) {
        if (!file) return;

        const allowedTypes = [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        if (!allowedTypes.includes(file.type)) {
            this.showError('Please select a CSV or Excel file');
            return;
        }

        this.selectedFile = file;
        this.showFileInfo(file);
        this.parseFileForMapping(file);
    }

    showFileInfo(file) {
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');

        fileName.textContent = file.name;
        fileSize.textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
        fileInfo.style.display = 'block';
    }

    parseFileForMapping(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                let headers = [];
                
                if (file.type === 'text/csv') {
                    const lines = e.target.result.split('\n');
                    headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                } else {
                    // For Excel files, we'll simulate header extraction
                    // In a real implementation, you'd use a library like SheetJS
                    this.showError('Excel file support requires additional libraries. Please use CSV format.');
                    return;
                }

                this.createColumnMapping(headers);
            } catch (error) {
                this.showError('Error parsing file: ' + error.message);
            }
        };
        
        reader.readAsText(file);
    }

    createColumnMapping(fileHeaders) {
        const mappingContainer = document.getElementById('mapping-container');
        const columnMapping = document.getElementById('column-mapping');
        const uploadActions = document.getElementById('upload-actions');

        let html = '';
        this.requiredColumns.forEach(requiredCol => {
            html += `
                <div class="mapping-item">
                    <span class="mapping-label">${requiredCol}:</span>
                    <select class="mapping-select" data-required="${requiredCol}">
                        <option value="">-- Select Column --</option>
                        ${fileHeaders.map(header => 
                            `<option value="${header}" ${header.toLowerCase().includes(requiredCol.toLowerCase()) ? 'selected' : ''}>${header}</option>`
                        ).join('')}
                    </select>
                </div>
            `;
        });

        mappingContainer.innerHTML = html;
        columnMapping.style.display = 'block';
        uploadActions.style.display = 'block';
    }

    processBlacklistUpload() {
        const mappingSelects = document.querySelectorAll('.mapping-select');
        const columnMap = {};
        
        mappingSelects.forEach(select => {
            const requiredCol = select.getAttribute('data-required');
            const selectedCol = select.value;
            if (selectedCol) {
                columnMap[requiredCol] = selectedCol;
            }
        });

        // Parse and process the file
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const lines = e.target.result.split('\n');
                const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                const newRecords = [];

                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    // Skip empty lines and lines with only commas
                    if (line && line.replace(/,/g, '').trim()) {
                        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));

                        // Skip lines that don't have enough data (likely incomplete)
                        if (values.length >= Object.keys(columnMap).length / 2) {
                            const record = {};

                            Object.keys(columnMap).forEach(requiredCol => {
                                const fileCol = columnMap[requiredCol];
                                const colIndex = headers.indexOf(fileCol);
                                record[requiredCol] = colIndex >= 0 ? values[colIndex] : '';
                            });

                            // Only add record if it has at least a name or PENID
                            if (record.Name || record.PENID) {
                                newRecords.push(record);
                            }
                        }
                    }
                }

                // Ask user if they want to replace or append data
                const shouldReplace = this.blacklistData.length > 0 ?
                    confirm(`You have ${this.blacklistData.length} existing records. Do you want to:\n\nOK = Replace all existing data\nCancel = Add to existing data`) :
                    true;

                if (shouldReplace) {
                    this.blacklistData = newRecords;
                    this.showSuccess(`Successfully replaced blacklist with ${newRecords.length} records`);
                } else {
                    this.blacklistData = [...this.blacklistData, ...newRecords];
                    this.showSuccess(`Successfully added ${newRecords.length} records to blacklist (Total: ${this.blacklistData.length})`);
                }

                this.saveBlacklistData();
                this.updateBlacklistDisplay();
                this.cancelUpload();
            } catch (error) {
                this.showError('Error processing file: ' + error.message);
            }
        };
        
        reader.readAsText(this.selectedFile);
    }

    cancelUpload() {
        document.getElementById('file-info').style.display = 'none';
        document.getElementById('column-mapping').style.display = 'none';
        document.getElementById('upload-actions').style.display = 'none';
        document.getElementById('blacklist-file').value = '';
        this.selectedFile = null;
    }

    clearBlacklist() {
        if (confirm('Are you sure you want to clear all blacklist data? This action cannot be undone.')) {
            this.blacklistData = [];
            this.saveBlacklistData();
            this.updateBlacklistDisplay();
            this.showSuccess('Blacklist cleared successfully');
        }
    }

    cleanBlacklistData() {
        const originalCount = this.blacklistData.length;

        // Remove invalid records (empty or missing essential data)
        this.blacklistData = this.blacklistData.filter(record =>
            record &&
            typeof record === 'object' &&
            (record.Name || record.PENID || record['Customer ID']) &&
            Object.keys(record).length > 0
        );

        // Remove duplicate records based on PENID or Name + Customer ID
        const seen = new Set();
        this.blacklistData = this.blacklistData.filter(record => {
            const key = record.PENID || `${record.Name}_${record['Customer ID']}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });

        const cleanedCount = this.blacklistData.length;
        const removedCount = originalCount - cleanedCount;

        this.saveBlacklistData();
        this.updateBlacklistDisplay();

        if (removedCount > 0) {
            this.showSuccess(`Cleaned blacklist data: removed ${removedCount} invalid/duplicate records. ${cleanedCount} valid records remaining.`);
        } else {
            this.showSuccess('No invalid records found. Blacklist data is already clean.');
        }
    }

    updateBlacklistDisplay() {
        const countElement = document.getElementById('blacklist-count');
        const tableBody = document.querySelector('#blacklist-table tbody');

        // Filter out any invalid records for accurate count
        const validRecords = this.blacklistData.filter(record =>
            record && (record.Name || record.PENID || record['Customer ID'])
        );

        countElement.textContent = `${validRecords.length} records`;
        
        let html = '';
        validRecords.slice(0, 100).forEach((record, index) => { // Show first 100 records
            html += `
                <tr>
                    <td>${record.PENID || 'N/A'}</td>
                    <td>${record.Name || 'N/A'}</td>
                    <td>${record['Customer ID'] || 'N/A'}</td>
                    <td>${record['NRC/BRN'] || 'N/A'}</td>
                    <td>${record.Industry || 'N/A'}</td>
                    <td>${record.Nationality || 'N/A'}</td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="app.removeBlacklistRecord(${index})">Remove</button>
                    </td>
                </tr>
            `;
        });
        
        if (validRecords.length > 100) {
            html += `<tr><td colspan="7" style="text-align: center; font-style: italic;">... and ${validRecords.length - 100} more records</td></tr>`;
        }
        
        tableBody.innerHTML = html || '<tr><td colspan="7" style="text-align: center;">No blacklist data available</td></tr>';
    }

    removeBlacklistRecord(index) {
        if (confirm('Are you sure you want to remove this record?')) {
            this.blacklistData.splice(index, 1);
            this.saveBlacklistData();
            this.updateBlacklistDisplay();
        }
    }

    // Data Management
    loadBlacklistData() {
        try {
            const data = localStorage.getItem('sanctionBlacklistData');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error loading blacklist data:', error);
            return [];
        }
    }

    saveBlacklistData() {
        try {
            localStorage.setItem('sanctionBlacklistData', JSON.stringify(this.blacklistData));
        } catch (error) {
            console.error('Error saving blacklist data:', error);
            this.showError('Error saving data to local storage');
        }
    }

    // Utility Methods
    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: ${type === 'error' ? '#dc3545' : '#28a745'};
            color: white;
            border-radius: 4px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SanctionScreeningApp();
});
