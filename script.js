// Sanction Screening Application JavaScript

class SanctionScreeningApp {
    constructor() {
        this.blacklistData = this.loadBlacklistData();
        this.requiredColumns = [
            'PENID', 'Reporting Date', 'Name', 'Customer ID', 'NRC/BRN', 'Industry', 
            'Segment', 'Group Code', 'Reporting Bank', 'Rating', 'Location', 
            'Customer Address 1', 'Customer Address 2', 'Phone 1', 'Phone 2', 
            'DOB/DOI', 'Date Field 2', 'GST', 'CIN', 'DIN', 'Nationality', 
            'Passport', 'Driver\'s License', 'Aadhaar(UIDAI)', 'Voter ID', 
            'Ration Card', 'Remarks', 'Text Field 1', 'Text Field 2', 
            'Text Field 3', 'I/E', 'Active / Inactive', 'Inactivation Date'
        ];
        this.init();
    }

    init() {
        this.setupTabNavigation();
        this.setupOnDemandScreening();
        this.setupBlacklistUpload();
        this.updateBlacklistDisplay();
    }

    // Tab Navigation
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                
                // Remove active class from all tabs and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                button.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }

    // On-demand Screening
    setupOnDemandScreening() {
        const screenBtn = document.getElementById('screen-btn');
        const clearBtn = document.getElementById('clear-btn');
        const searchValue = document.getElementById('search-value');
        const searchTypeButtons = document.querySelectorAll('.search-type-btn');

        screenBtn.addEventListener('click', () => this.performScreening());
        clearBtn.addEventListener('click', () => this.clearScreeningForm());

        // Search type button handling
        searchTypeButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                searchTypeButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
            });
        });

        // Enter key support
        searchValue.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performScreening();
            }
        });
    }

    performScreening() {
        const activeSearchTypeBtn = document.querySelector('.search-type-btn.active');
        const searchType = activeSearchTypeBtn ? activeSearchTypeBtn.getAttribute('data-type') : 'name';
        const searchValue = document.getElementById('search-value').value.trim();
        const matchType = document.getElementById('match-type').value;
        const resultsContainer = document.getElementById('results-container');

        if (!searchValue) {
            this.showError('Please enter a search value');
            return;
        }

        // Show loading
        resultsContainer.innerHTML = '<div class="loading"></div><p>Screening in progress...</p>';

        // Simulate screening delay
        setTimeout(() => {
            const results = this.searchBlacklist(searchType, searchValue, matchType);
            this.displayScreeningResults(results, searchType, searchValue);
        }, 1000);
    }

    searchBlacklist(searchType, searchValue, matchType) {
        const results = [];
        const searchLower = searchValue.toLowerCase();

        // Filter valid records for searching
        const validRecords = this.blacklistData.filter(record =>
            record && (record.Name || record.PENID || record['Customer ID'])
        );

        validRecords.forEach((record, index) => {
            let fieldValue = '';
            
            switch (searchType) {
                case 'name':
                    fieldValue = record.Name || '';
                    break;
                case 'nationality':
                    fieldValue = record.Nationality || '';
                    break;
                case 'nrc':
                case 'brn':
                    fieldValue = record['NRC/BRN'] || '';
                    break;
                case 'entity':
                    fieldValue = record.Name || ''; // Assuming entity name is in Name field
                    break;
                case 'dob':
                    fieldValue = record['DOB/DOI'] || '';
                    break;
                case 'phone':
                    fieldValue = (record['Phone 1'] || '') + ' ' + (record['Phone 2'] || '');
                    break;
            }

            fieldValue = fieldValue.toLowerCase();

            let isMatch = false;
            switch (matchType) {
                case 'exact':
                    isMatch = fieldValue === searchLower;
                    break;
                case 'partial':
                    isMatch = fieldValue.includes(searchLower);
                    break;
                case 'fuzzy':
                    isMatch = this.fuzzyMatch(fieldValue, searchLower);
                    break;
            }

            if (isMatch) {
                results.push({
                    ...record,
                    matchScore: this.calculateMatchScore(fieldValue, searchLower, matchType)
                });
            }
        });

        return results.sort((a, b) => b.matchScore - a.matchScore);
    }

    fuzzyMatch(str1, str2) {
        const threshold = 0.7;
        const similarity = this.calculateSimilarity(str1, str2);
        return similarity >= threshold;
    }

    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    calculateMatchScore(fieldValue, searchValue, matchType) {
        switch (matchType) {
            case 'exact':
                return fieldValue === searchValue ? 100 : 0;
            case 'partial':
                return fieldValue.includes(searchValue) ? 80 : 0;
            case 'fuzzy':
                return Math.round(this.calculateSimilarity(fieldValue, searchValue) * 100);
            default:
                return 0;
        }
    }

    displayScreeningResults(results, searchType, searchValue) {
        const resultsContainer = document.getElementById('results-container');
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="status-success">
                    <h4>✓ No Matches Found</h4>
                    <p>No records found matching "${searchValue}" in ${searchType} field.</p>
                    <p>This entity appears to be clear for processing.</p>
                </div>
            `;
            return;
        }

        let html = `
            <div class="status-warning">
                <h4>⚠ Potential Matches Found</h4>
                <p>${results.length} record(s) found matching "${searchValue}" in ${searchType} field.</p>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Match Score</th>
                        <th>Name</th>
                        <th>PENID</th>
                        <th>Customer ID</th>
                        <th>NRC/BRN</th>
                        <th>Nationality</th>
                        <th>Industry</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
        `;

        results.forEach(record => {
            const status = record['Active / Inactive'] || 'Unknown';
            const statusClass = status.toLowerCase() === 'active' ? 'status-error' : 'status-warning';
            
            html += `
                <tr>
                    <td><span class="status-warning">${record.matchScore}%</span></td>
                    <td>${record.Name || 'N/A'}</td>
                    <td>${record.PENID || 'N/A'}</td>
                    <td>${record['Customer ID'] || 'N/A'}</td>
                    <td>${record['NRC/BRN'] || 'N/A'}</td>
                    <td>${record.Nationality || 'N/A'}</td>
                    <td>${record.Industry || 'N/A'}</td>
                    <td><span class="${statusClass}">${status}</span></td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        resultsContainer.innerHTML = html;
    }

    clearScreeningForm() {
        // Reset search type buttons
        const searchTypeButtons = document.querySelectorAll('.search-type-btn');
        searchTypeButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelector('.search-type-btn[data-type="name"]').classList.add('active');

        document.getElementById('search-value').value = '';
        document.getElementById('match-type').value = 'exact';
        document.getElementById('results-container').innerHTML = '<p class="no-results">No screening performed yet.</p>';
    }

    // Blacklist Upload
    setupBlacklistUpload() {
        const fileInput = document.getElementById('blacklist-file');
        const dropzone = document.getElementById('upload-dropzone');
        const uploadBtn = document.getElementById('upload-btn');
        const cancelBtn = document.getElementById('cancel-upload-btn');
        const clearBlacklistBtn = document.getElementById('clear-blacklist-btn');
        const cleanBlacklistBtn = document.getElementById('clean-blacklist-btn');

        // File input and dropzone
        dropzone.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));

        // Drag and drop
        dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.classList.add('dragover');
        });

        dropzone.addEventListener('dragleave', () => {
            dropzone.classList.remove('dragover');
        });

        dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files[0]);
        });

        // Buttons
        uploadBtn.addEventListener('click', () => this.processBlacklistUpload());
        cancelBtn.addEventListener('click', () => this.cancelUpload());
        clearBlacklistBtn.addEventListener('click', () => this.clearBlacklist());
        cleanBlacklistBtn.addEventListener('click', () => this.cleanBlacklistData());
    }

    handleFileSelect(file) {
        if (!file) return;

        const allowedTypes = [
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];

        if (!allowedTypes.includes(file.type)) {
            this.showError('Please select a CSV or Excel file');
            return;
        }

        this.selectedFile = file;
        this.showFileInfo(file);
        this.parseFileForMapping(file);
    }

    showFileInfo(file) {
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');

        fileName.textContent = file.name;
        fileSize.textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
        fileInfo.style.display = 'block';
    }

    parseFileForMapping(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                let headers = [];
                
                if (file.type === 'text/csv') {
                    const lines = e.target.result.split('\n');
                    headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                } else {
                    // For Excel files, we'll simulate header extraction
                    // In a real implementation, you'd use a library like SheetJS
                    this.showError('Excel file support requires additional libraries. Please use CSV format.');
                    return;
                }

                this.createColumnMapping(headers);
            } catch (error) {
                this.showError('Error parsing file: ' + error.message);
            }
        };
        
        reader.readAsText(file);
    }

    createColumnMapping(fileHeaders) {
        const mappingContainer = document.getElementById('mapping-container');
        const columnMapping = document.getElementById('column-mapping');
        const uploadActions = document.getElementById('upload-actions');

        let html = '';
        this.requiredColumns.forEach(requiredCol => {
            html += `
                <div class="mapping-item">
                    <span class="mapping-label">${requiredCol}:</span>
                    <select class="mapping-select" data-required="${requiredCol}">
                        <option value="">-- Select Column --</option>
                        ${fileHeaders.map(header => 
                            `<option value="${header}" ${header.toLowerCase().includes(requiredCol.toLowerCase()) ? 'selected' : ''}>${header}</option>`
                        ).join('')}
                    </select>
                </div>
            `;
        });

        mappingContainer.innerHTML = html;
        columnMapping.style.display = 'block';
        uploadActions.style.display = 'block';
    }

    processBlacklistUpload() {
        const mappingSelects = document.querySelectorAll('.mapping-select');
        const columnMap = {};
        
        mappingSelects.forEach(select => {
            const requiredCol = select.getAttribute('data-required');
            const selectedCol = select.value;
            if (selectedCol) {
                columnMap[requiredCol] = selectedCol;
            }
        });

        // Parse and process the file
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const lines = e.target.result.split('\n');
                const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                const newRecords = [];

                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    // Skip empty lines and lines with only commas
                    if (line && line.replace(/,/g, '').trim()) {
                        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));

                        // Skip lines that don't have enough data (likely incomplete)
                        if (values.length >= Object.keys(columnMap).length / 2) {
                            const record = {};

                            Object.keys(columnMap).forEach(requiredCol => {
                                const fileCol = columnMap[requiredCol];
                                const colIndex = headers.indexOf(fileCol);
                                record[requiredCol] = colIndex >= 0 ? values[colIndex] : '';
                            });

                            // Only add record if it has at least a name or PENID
                            if (record.Name || record.PENID) {
                                newRecords.push(record);
                            }
                        }
                    }
                }

                // Ask user if they want to replace or append data
                const shouldReplace = this.blacklistData.length > 0 ?
                    confirm(`You have ${this.blacklistData.length} existing records. Do you want to:\n\nOK = Replace all existing data\nCancel = Add to existing data`) :
                    true;

                if (shouldReplace) {
                    this.blacklistData = newRecords;
                    this.showSuccess(`Successfully replaced blacklist with ${newRecords.length} records`);
                } else {
                    this.blacklistData = [...this.blacklistData, ...newRecords];
                    this.showSuccess(`Successfully added ${newRecords.length} records to blacklist (Total: ${this.blacklistData.length})`);
                }

                this.saveBlacklistData();
                this.updateBlacklistDisplay();
                this.cancelUpload();
            } catch (error) {
                this.showError('Error processing file: ' + error.message);
            }
        };
        
        reader.readAsText(this.selectedFile);
    }

    cancelUpload() {
        document.getElementById('file-info').style.display = 'none';
        document.getElementById('column-mapping').style.display = 'none';
        document.getElementById('upload-actions').style.display = 'none';
        document.getElementById('blacklist-file').value = '';
        this.selectedFile = null;
    }

    clearBlacklist() {
        if (confirm('Are you sure you want to clear all blacklist data? This action cannot be undone.')) {
            this.blacklistData = [];
            this.saveBlacklistData();
            this.updateBlacklistDisplay();
            this.showSuccess('Blacklist cleared successfully');
        }
    }

    cleanBlacklistData() {
        const originalCount = this.blacklistData.length;

        // Remove invalid records (empty or missing essential data)
        this.blacklistData = this.blacklistData.filter(record =>
            record &&
            typeof record === 'object' &&
            (record.Name || record.PENID || record['Customer ID']) &&
            Object.keys(record).length > 0
        );

        // Remove duplicate records based on PENID or Name + Customer ID
        const seen = new Set();
        this.blacklistData = this.blacklistData.filter(record => {
            const key = record.PENID || `${record.Name}_${record['Customer ID']}`;
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });

        const cleanedCount = this.blacklistData.length;
        const removedCount = originalCount - cleanedCount;

        this.saveBlacklistData();
        this.updateBlacklistDisplay();

        if (removedCount > 0) {
            this.showSuccess(`Cleaned blacklist data: removed ${removedCount} invalid/duplicate records. ${cleanedCount} valid records remaining.`);
        } else {
            this.showSuccess('No invalid records found. Blacklist data is already clean.');
        }
    }

    updateBlacklistDisplay() {
        const countElement = document.getElementById('blacklist-count');
        const tableBody = document.querySelector('#blacklist-table tbody');

        // Filter out any invalid records for accurate count
        const validRecords = this.blacklistData.filter(record =>
            record && (record.Name || record.PENID || record['Customer ID'])
        );

        countElement.textContent = `${validRecords.length} records`;
        
        let html = '';
        validRecords.slice(0, 100).forEach((record, index) => { // Show first 100 records
            html += `
                <tr>
                    <td>${record.PENID || 'N/A'}</td>
                    <td>${record.Name || 'N/A'}</td>
                    <td>${record['Customer ID'] || 'N/A'}</td>
                    <td>${record['NRC/BRN'] || 'N/A'}</td>
                    <td>${record.Industry || 'N/A'}</td>
                    <td>${record.Nationality || 'N/A'}</td>
                    <td>
                        <button class="btn btn-danger btn-sm" onclick="app.removeBlacklistRecord(${index})">Remove</button>
                    </td>
                </tr>
            `;
        });
        
        if (validRecords.length > 100) {
            html += `<tr><td colspan="7" style="text-align: center; font-style: italic;">... and ${validRecords.length - 100} more records</td></tr>`;
        }
        
        tableBody.innerHTML = html || '<tr><td colspan="7" style="text-align: center;">No blacklist data available</td></tr>';
    }

    removeBlacklistRecord(index) {
        if (confirm('Are you sure you want to remove this record?')) {
            this.blacklistData.splice(index, 1);
            this.saveBlacklistData();
            this.updateBlacklistDisplay();
        }
    }

    // Data Management
    loadBlacklistData() {
        try {
            const data = localStorage.getItem('sanctionBlacklistData');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error loading blacklist data:', error);
            return [];
        }
    }

    saveBlacklistData() {
        try {
            localStorage.setItem('sanctionBlacklistData', JSON.stringify(this.blacklistData));
        } catch (error) {
            console.error('Error saving blacklist data:', error);
            this.showError('Error saving data to local storage');
        }
    }

    // Utility Methods
    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: ${type === 'error' ? '#dc3545' : '#28a745'};
            color: white;
            border-radius: 4px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SanctionScreeningApp();
});
