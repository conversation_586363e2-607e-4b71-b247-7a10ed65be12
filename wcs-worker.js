// Web Worker for WCSPremium file processing
// This worker handles large file operations without blocking the main UI thread

class WCSPremiumWorker {
    constructor() {
        this.isProcessing = false;
        this.searchIndex = new Map(); // In-memory search index
        this.totalRecords = 0;
        this.processedRecords = 0;
        this.chunkSize = 1024 * 1024; // 1MB chunks
        this.maxMemoryRecords = 50000; // Maximum records to keep in memory
        this.currentData = []; // Current chunk of data in memory
        this.fileHandle = null;
    }

    // Initialize the worker and set up message handlers
    init() {
        self.onmessage = (event) => {
            const { type, data } = event.data;
            
            switch (type) {
                case 'LOAD_FILE':
                    this.loadFile(data.file);
                    break;
                case 'SEARCH':
                    this.performSearch(data.searchType, data.searchValue, data.matchType, data.searchId);
                    break;
                case 'GET_STATUS':
                    this.sendStatus();
                    break;
                case 'CLEAR_DATA':
                    this.clearData();
                    break;
                default:
                    this.sendMessage('ERROR', { message: 'Unknown command: ' + type });
            }
        };
    }

    // Load and process the WCSPremium file
    async loadFile(file) {
        try {
            this.isProcessing = true;
            this.sendMessage('LOAD_STARTED', { fileName: file.name, fileSize: file.size });
            
            // Reset counters
            this.totalRecords = 0;
            this.processedRecords = 0;
            this.searchIndex.clear();
            this.currentData = [];
            
            // Process file in chunks
            await this.processFileInChunks(file);
            
            this.isProcessing = false;
            this.sendMessage('LOAD_COMPLETED', { 
                totalRecords: this.totalRecords,
                indexSize: this.searchIndex.size 
            });
            
        } catch (error) {
            this.isProcessing = false;
            this.sendMessage('ERROR', { message: 'Failed to load file: ' + error.message });
        }
    }

    // Process file in manageable chunks
    async processFileInChunks(file) {
        const reader = new FileReader();
        let offset = 0;
        let remainder = '';
        let headerProcessed = false;
        let headers = [];

        while (offset < file.size) {
            const chunk = file.slice(offset, offset + this.chunkSize);
            const chunkText = await this.readChunk(chunk);
            
            // Combine with remainder from previous chunk
            const fullText = remainder + chunkText;
            const lines = fullText.split('\n');
            
            // Keep the last incomplete line as remainder
            remainder = lines.pop() || '';
            
            // Process headers from first chunk
            if (!headerProcessed && lines.length > 0) {
                headers = this.parseHeaders(lines[0]);
                lines.shift(); // Remove header line
                headerProcessed = true;
            }
            
            // Process data lines
            for (const line of lines) {
                if (line.trim()) {
                    this.processDataLine(line, headers);
                }
            }
            
            offset += this.chunkSize;
            
            // Send progress update
            const progress = Math.min((offset / file.size) * 100, 100);
            this.sendMessage('LOAD_PROGRESS', { 
                progress: progress,
                processedRecords: this.processedRecords 
            });
            
            // Allow other operations to run
            await this.sleep(1);
        }
        
        // Process final remainder if exists
        if (remainder.trim()) {
            this.processDataLine(remainder, headers);
        }
    }

    // Read a file chunk as text
    readChunk(chunk) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            reader.readAsText(chunk);
        });
    }

    // Parse CSV headers (tab-separated)
    parseHeaders(headerLine) {
        return headerLine.split('\t').map(h => h.trim());
    }

    // Process a single data line
    processDataLine(line, headers) {
        try {
            const values = line.split('\t');
            if (values.length < headers.length / 2) return; // Skip incomplete lines
            
            const record = {};
            headers.forEach((header, index) => {
                record[header] = values[index] ? values[index].trim() : '';
            });
            
            // Only process records with essential data
            if (record.UID || record['LAST NAME'] || record['FIRST NAME']) {
                this.indexRecord(record);
                this.totalRecords++;
                this.processedRecords++;
                
                // Manage memory by keeping only recent records
                if (this.currentData.length >= this.maxMemoryRecords) {
                    this.currentData.shift(); // Remove oldest record
                }
                this.currentData.push(record);
            }
        } catch (error) {
            // Skip malformed lines
            console.warn('Skipped malformed line:', error.message);
        }
    }

    // Create search index for fast lookups
    indexRecord(record) {
        const uid = record.UID || this.processedRecords.toString();
        
        // Index searchable fields
        const searchableFields = [
            'LAST NAME', 'FIRST NAME', 'ALIASES', 'CITIZENSHIP', 
            'COUNTRIES', 'IDENTIFICATION NUMBERS', 'DOB', 'DOBS'
        ];
        
        searchableFields.forEach(field => {
            const value = record[field];
            if (value) {
                const normalizedValue = value.toLowerCase();
                
                // Index full value
                if (!this.searchIndex.has(normalizedValue)) {
                    this.searchIndex.set(normalizedValue, new Set());
                }
                this.searchIndex.get(normalizedValue).add(uid);
                
                // Index individual words for partial matching
                const words = normalizedValue.split(/[;\s,]+/);
                words.forEach(word => {
                    if (word.length > 2) { // Skip very short words
                        if (!this.searchIndex.has(word)) {
                            this.searchIndex.set(word, new Set());
                        }
                        this.searchIndex.get(word).add(uid);
                    }
                });
            }
        });
    }

    // Perform search across the indexed data
    async performSearch(searchType, searchValue, matchType, searchId) {
        try {
            this.sendMessage('SEARCH_STARTED', { searchType, searchValue, matchType, searchId });
            
            const results = [];
            const searchLower = searchValue.toLowerCase();
            
            // Map search types to WCS fields
            const fieldMapping = {
                'name': ['LAST NAME', 'FIRST NAME', 'ALIASES'],
                'nationality': ['CITIZENSHIP', 'COUNTRIES'],
                'nrc': ['IDENTIFICATION NUMBERS'],
                'brn': ['IDENTIFICATION NUMBERS'],
                'entity': ['LAST NAME', 'FIRST NAME', 'ALIASES'],
                'dob': ['DOB', 'DOBS'],
                'phone': ['IDENTIFICATION NUMBERS'] // Phone numbers might be in ID numbers
            };
            
            const fieldsToSearch = fieldMapping[searchType] || ['LAST NAME', 'FIRST NAME'];
            
            // Search through current data in memory
            for (const record of this.currentData) {
                let isMatch = false;
                let matchScore = 0;
                
                for (const field of fieldsToSearch) {
                    const fieldValue = (record[field] || '').toLowerCase();
                    
                    if (fieldValue) {
                        switch (matchType) {
                            case 'exact':
                                if (fieldValue === searchLower) {
                                    isMatch = true;
                                    matchScore = 100;
                                }
                                break;
                            case 'partial':
                                if (fieldValue.includes(searchLower)) {
                                    isMatch = true;
                                    matchScore = 80;
                                }
                                break;
                            case 'fuzzy':
                                const similarity = this.calculateSimilarity(fieldValue, searchLower);
                                if (similarity >= 0.7) {
                                    isMatch = true;
                                    matchScore = Math.round(similarity * 100);
                                }
                                break;
                        }
                        
                        if (isMatch) break;
                    }
                }
                
                if (isMatch) {
                    results.push({
                        ...record,
                        matchScore: matchScore,
                        source: 'WCSPremium'
                    });
                }
            }
            
            // Sort by match score
            results.sort((a, b) => b.matchScore - a.matchScore);
            
            this.sendMessage('SEARCH_COMPLETED', {
                results: results.slice(0, 100), // Limit to top 100 results
                totalMatches: results.length,
                searchId: searchId
            });
            
        } catch (error) {
            this.sendMessage('ERROR', { message: 'Search failed: ' + error.message });
        }
    }

    // Calculate string similarity using Levenshtein distance
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    // Levenshtein distance calculation
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    // Send status information
    sendStatus() {
        this.sendMessage('STATUS', {
            isProcessing: this.isProcessing,
            totalRecords: this.totalRecords,
            processedRecords: this.processedRecords,
            indexSize: this.searchIndex.size,
            memoryRecords: this.currentData.length
        });
    }

    // Clear all data
    clearData() {
        this.searchIndex.clear();
        this.currentData = [];
        this.totalRecords = 0;
        this.processedRecords = 0;
        this.sendMessage('DATA_CLEARED', {});
    }

    // Send message to main thread
    sendMessage(type, data) {
        self.postMessage({ type, data });
    }

    // Sleep utility for non-blocking processing
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the worker
const worker = new WCSPremiumWorker();
worker.init();
