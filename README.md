# Sanction Screening Application

A client-side web application for sanction screening with no external dependencies. Built with pure HTML, CSS, and JavaScript.

## Features

### ✅ Implemented Features

#### 1. On-demand Screening
- Search by multiple criteria: Name, Nationality, NRC, BRN, Entity Name, Date of Birth, Phone Number
- Easy-to-use button interface for search type selection
- Three match types: Exact Match, Partial Match, Fuzzy Match
- Real-time screening against uploaded blacklist
- Match scoring and risk assessment
- Clear visual indication of screening results

#### 2. Blacklist Upload Configuration
- Support for CSV file uploads
- Drag and drop file interface
- Column mapping for all 32 required fields:
  - PENID, Reporting Date, Name, Customer ID, NRC/BRN
  - Industry, Segment, Group Code, Reporting Bank, Rating
  - Location, Customer Address 1 & 2, Phone 1 & 2
  - DOB/DOI, Date Field 2, GST, CIN, DIN
  - Nationality, Passport, Driver's License
  - Aadhaar(UIDAI), Voter ID, Ration Card
  - Remarks, Text Fields 1-3, I/E
  - Active/Inactive status, Inactivation Date
- Data validation and error handling
- Local storage for data persistence

#### 3. Data Management
- View current blacklist records
- Remove individual records
- Clear entire blacklist
- Record count display
- Persistent storage using localStorage

### 🚧 Placeholder Tabs (For Future Implementation)
- Bulk Screening
- SWIFT Screening

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server or additional software required

### Installation
1. Download all files to a local directory
2. Open `index.html` in your web browser
3. The application will load immediately

### Usage

#### Setting Up Blacklist Data
1. Click on the "Blacklist Upload" tab
2. Use the provided `sample_blacklist.csv` file or create your own CSV file
3. Click the upload area or drag and drop your CSV file
4. Map the columns from your file to the required blacklist fields
5. Click "Upload Blacklist" to import the data

#### Performing On-demand Screening
1. Go to the "On-demand Screening" tab
2. Click on the search type button (Name, Nationality, NRC, etc.)
3. Enter the search value
4. Choose match type (Exact, Partial, or Fuzzy)
5. Click "Screen" to perform the search
6. Review results with match scores and risk indicators

## File Structure

```
sanction-screening/
├── index.html          # Main application file
├── styles.css          # Application styling
├── script.js           # Application logic
├── sample_blacklist.csv # Sample data for testing
└── README.md           # This file
```

## Technical Details

### Data Storage
- Uses browser's localStorage for data persistence
- No server-side database required
- Data persists between browser sessions

### Search Algorithms
- **Exact Match**: Perfect string matching
- **Partial Match**: Substring matching
- **Fuzzy Match**: Levenshtein distance algorithm with 70% similarity threshold

### File Support
- CSV files (recommended)
- Excel files (.xlsx, .xls) - requires additional libraries for full support

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Sample Data

The included `sample_blacklist.csv` contains 10 sample records with various risk profiles:
- High-risk individuals (money laundering, terrorism financing)
- Medium-risk entities (sanctions violations, corruption)
- Low-risk records (export violations)
- Mix of active and inactive records

## Security Considerations

- All data is stored locally in the browser
- No data is transmitted to external servers
- Clear browser data to remove all stored information
- Suitable for sensitive screening operations

## Customization

### Adding New Search Fields
1. Update the search type dropdown in `index.html`
2. Add corresponding case in the `searchBlacklist` method in `script.js`
3. Update the display logic as needed

### Modifying Required Columns
1. Update the `requiredColumns` array in `script.js`
2. Adjust the blacklist table headers in `index.html`
3. Update the display methods accordingly

## Troubleshooting

### Common Issues

**File Upload Not Working**
- Ensure file is in CSV format
- Check file size (large files may cause browser slowdown)
- Verify CSV structure matches expected format

**Search Not Finding Results**
- Check if blacklist data is uploaded
- Verify search value format
- Try different match types

**Data Not Persisting**
- Ensure localStorage is enabled in browser
- Check available storage space
- Clear browser cache if issues persist

## Future Enhancements

The application is designed for easy extension:
- Scheduled screening with cron-like functionality
- Bulk file screening capabilities
- SWIFT message screening
- Export functionality for results
- Advanced reporting and analytics
- Integration with external sanction lists

## Support

For technical issues or feature requests, please refer to the application documentation or contact your system administrator.
