# Sanction Screening Application

A client-side web application for sanction screening with no external dependencies. Built with pure HTML, CSS, and JavaScript.

## Features

### ✅ Implemented Features

#### 1. On-demand Screening
- Search by multiple criteria: Name, Nationality, NRC, BRN, Entity Name, Date of Birth, Phone Number
- Easy-to-use button interface for search type selection
- Three match types: Exact Match, Partial Match, Fuzzy Match
- **Dual-source screening**: Searches both regular blacklist AND WCSPremium simultaneously
- Advanced match scoring and risk assessment
- Clear visual indication of screening results with source identification
- Real-time status indicators for both data sources

#### 2. Blacklist Upload Configuration
- Support for CSV file uploads
- Drag and drop file interface
- Column mapping for all 32 required fields:
  - PENID, Reporting Date, Name, Customer ID, NRC/BRN
  - Industry, Segment, Group Code, Reporting Bank, Rating
  - Location, Customer Address 1 & 2, Phone 1 & 2
  - DOB/DOI, Date Field 2, GST, CIN, DIN
  - Nationality, Passport, Driver's License
  - Aadhaar(UIDAI), Voter ID, Ration Card
  - Remarks, Text Fields 1-3, I/E
  - Active/Inactive status, Inactivation Date
- Data validation and error handling
- Local storage for data persistence

#### 3. WCSPremium Integration
- **Large file handling**: Efficiently processes 5GB+ WCSPremium files
- **Background processing**: Uses Web Workers to avoid blocking the UI
- **Chunked loading**: Streams large files in manageable chunks
- **Memory optimization**: Smart indexing and memory management
- **Auto-loading**: Automatically detects and loads WCSPremium file from lists folder
- **Real-time status**: Live progress indicators during file loading

#### 4. Data Management
- View current blacklist records
- Remove individual records
- Clear entire blacklist
- Record count display
- Persistent storage using localStorage
- **WCSPremium management**: Load, reload, and clear large datasets
- **Dual-source status**: Monitor both data sources simultaneously

### 🚧 Placeholder Tabs (For Future Implementation)
- Bulk Screening
- SWIFT Screening

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- **For full WCSPremium functionality**: Local HTTP server (Python recommended)
- **For basic functionality**: No server required (fallback mode)

### Installation

#### Option 1: Full Functionality (Recommended)
1. Download all files to a local directory
2. **Start local HTTP server**:
   - **Windows**: Double-click `start-server.bat`
   - **Python users**: Run `python -m http.server 8000` in the project directory
   - **Node.js users**: Run `npx http-server -p 8000` in the project directory
3. Open `http://localhost:8000` in your web browser
4. Full WCSPremium functionality will be available

#### Option 2: Fallback Mode (Limited)
1. Download all files to a local directory
2. Open `index.html` directly in your web browser
3. WCSPremium will work in fallback mode (limited to 10,000 records)
4. Regular blacklist functionality remains fully available

### Usage

#### Setting Up Blacklist Data
1. Click on the "Blacklist Upload" tab
2. Use the provided `sample_blacklist.csv` file or create your own CSV file
3. Click the upload area or drag and drop your CSV file
4. Map the columns from your file to the required blacklist fields
5. Click "Upload Blacklist" to import the data

#### Managing WCSPremium Data
1. Go to the "Blacklist Upload" tab
2. Check the WCSPremium status in the management section
3. If not loaded, click "Load WCS File" to select the premium-world-check.csv file
4. Monitor loading progress (may take several minutes for large files)
5. Use "Reload" to refresh from the lists folder or "Clear WCS Data" to remove

#### Performing On-demand Screening
1. Go to the "On-demand Screening" tab
2. Check that both data sources show "Ready" or "Loaded" status
3. Click on the search type button (Name, Nationality, NRC, etc.)
4. Enter the search value
5. Choose match type (Exact, Partial, or Fuzzy)
6. Click "Screen" to perform the search across both sources
7. Review results with match scores, source identification, and risk indicators

## File Structure

```
sanction-screening/
├── index.html              # Main application file
├── styles.css              # Application styling
├── script.js               # Main application logic
├── wcs-worker.js           # Web Worker for WCSPremium processing
├── start-server.bat        # Windows batch file to start HTTP server
├── sample_blacklist.csv    # Sample data for testing
├── lists/
│   └── premium-world-check.csv  # WCSPremium data file (5GB+)
└── README.md               # This file
```

## Technical Details

### Data Storage
- **Regular Blacklist**: Uses browser's localStorage for data persistence
- **WCSPremium**: Processed in-memory with smart indexing for fast searches
- No server-side database required
- Regular blacklist data persists between browser sessions
- WCSPremium data loads fresh each session for optimal performance

### Search Algorithms
- **Exact Match**: Perfect string matching
- **Partial Match**: Substring matching
- **Fuzzy Match**: Levenshtein distance algorithm with 70% similarity threshold

### File Support
- **Regular Blacklist**: CSV files (recommended), Excel files (.xlsx, .xls) - requires additional libraries for full support
- **WCSPremium**: Large CSV files (5GB+) with tab-separated values
- Optimized for extremely large datasets without browser performance issues

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Sample Data

The included `sample_blacklist.csv` contains 10 sample records with various risk profiles:
- High-risk individuals (money laundering, terrorism financing)
- Medium-risk entities (sanctions violations, corruption)
- Low-risk records (export violations)
- Mix of active and inactive records

## Security Considerations

- All data is stored locally in the browser
- No data is transmitted to external servers
- Clear browser data to remove all stored information
- Suitable for sensitive screening operations

## Customization

### Adding New Search Fields
1. Update the search type dropdown in `index.html`
2. Add corresponding case in the `searchBlacklist` method in `script.js`
3. Update the display logic as needed

### Modifying Required Columns
1. Update the `requiredColumns` array in `script.js`
2. Adjust the blacklist table headers in `index.html`
3. Update the display methods accordingly

## Troubleshooting

### Common Issues

**WCSPremium Not Loading (Web Worker Error)**
- **Solution**: Use HTTP server instead of opening file directly
- Run `start-server.bat` (Windows) or `python -m http.server 8000`
- Open `http://localhost:8000` instead of file://
- Alternatively, use fallback mode with limited functionality

**File Upload Not Working**
- Ensure file is in CSV format
- Check file size (large files may cause browser slowdown)
- Verify CSV structure matches expected format

**Search Not Finding Results**
- Check if blacklist data is uploaded
- Verify search value format
- Try different match types
- Ensure both data sources are loaded (check status indicators)

**Data Not Persisting**
- Ensure localStorage is enabled in browser
- Check available storage space
- Clear browser cache if issues persist
- Note: WCSPremium data loads fresh each session

**Performance Issues with Large Files**
- Use HTTP server mode for optimal performance
- In fallback mode, only first 10,000 WCS records are loaded
- Consider using smaller test files for development

## Future Enhancements

The application is designed for easy extension:
- Scheduled screening with cron-like functionality
- Bulk file screening capabilities
- SWIFT message screening
- Export functionality for results
- Advanced reporting and analytics
- Integration with external sanction lists

## Support

For technical issues or feature requests, please refer to the application documentation or contact your system administrator.
