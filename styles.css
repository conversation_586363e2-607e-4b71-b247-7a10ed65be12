/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Header */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

header h1 {
    font-size: 2rem;
    font-weight: 300;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    overflow-x: auto;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background-color: #e9ecef;
    color: #495057;
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background-color: white;
}

/* Content */
.content {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Forms */
.screening-form {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

/* Search Type Buttons */
.search-type-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.search-type-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    background-color: white;
    color: #6c757d;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.search-type-btn:hover {
    border-color: #667eea;
    color: #667eea;
    background-color: #f0f2ff;
}

.search-type-btn.active {
    border-color: #667eea;
    background-color: #667eea;
    color: white;
}

.search-type-btn.active:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

/* Results Section */
.results-section {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.results-section h3 {
    margin-bottom: 1rem;
    color: #495057;
}

.no-results {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

/* Upload Section */
.upload-section {
    margin-bottom: 2rem;
}

.upload-dropzone {
    border: 2px dashed #ced4da;
    border-radius: 8px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.upload-dropzone:hover {
    border-color: #667eea;
    background-color: #f0f2ff;
}

.upload-dropzone.dragover {
    border-color: #667eea;
    background-color: #e6eaff;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.file-types {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

/* File Info */
.file-info {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 1rem;
    margin: 1rem 0;
}

/* Column Mapping */
.column-mapping {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
}

.mapping-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.mapping-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.mapping-label {
    font-weight: 500;
    min-width: 120px;
    color: #495057;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Blacklist Management */
.blacklist-management {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.blacklist-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.blacklist-actions {
    display: flex;
    gap: 0.5rem;
}

.blacklist-table-container {
    max-height: 400px;
    overflow-y: auto;
}

/* Placeholder Content */
.placeholder-content {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.placeholder-content p {
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }

    .content {
        padding: 1rem;
    }

    .screening-form {
        padding: 1rem;
    }

    .search-type-buttons {
        justify-content: center;
    }

    .search-type-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        margin-bottom: 0.5rem;
        margin-right: 0;
    }

    .blacklist-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .mapping-container {
        grid-template-columns: 1fr;
    }
}

/* Loading and Status Indicators */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-success {
    color: #28a745;
    font-weight: 500;
}

.status-error {
    color: #dc3545;
    font-weight: 500;
}

.status-warning {
    color: #ffc107;
    font-weight: 500;
}
